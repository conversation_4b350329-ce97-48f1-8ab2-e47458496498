-- Create FingerprintBridge tables manually
USE studentfinger;

-- Create fingerprint_import_logs table
CREATE TABLE IF NOT EXISTS `fingerprint_import_logs` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `import_type` ENUM('manual', 'auto', 'scheduled') NOT NULL DEFAULT 'manual',
    `start_date` DATETIME NULL,
    `end_date` DATETIME NULL,
    `status` ENUM('pending', 'running', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    `total_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `processed_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `inserted_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `updated_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `skipped_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `error_records` INT(11) UNSIGNED NOT NULL DEFAULT 0,
    `start_time` DATETIME NULL,
    `end_time` DATETIME NULL,
    `duration` INT(11) UNSIGNED NULL COMMENT 'Duration in seconds',
    `error_message` TEXT NULL,
    `settings` JSON NULL,
    `user_id` INT(11) UNSIGNED NULL,
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    KEY `idx_status_created` (`status`, `created_at`),
    KEY `idx_import_type` (`import_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create student_pin_mapping table
CREATE TABLE IF NOT EXISTS `student_pin_mapping` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `pin` VARCHAR(32) NOT NULL COMMENT 'PIN from fingerprint machine',
    `student_id` INT(11) UNSIGNED NOT NULL COMMENT 'Reference to students.student_id',
    `rfid_card` VARCHAR(50) NULL COMMENT 'RFID card number if available',
    `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1=Active, 0=Inactive',
    `notes` VARCHAR(255) NULL,
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    `deleted_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_pin` (`pin`),
    UNIQUE KEY `uk_student_id` (`student_id`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create fingerprint_import_settings table
CREATE TABLE IF NOT EXISTS `fingerprint_import_settings` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `setting_key` VARCHAR(100) NOT NULL,
    `setting_value` TEXT NULL,
    `setting_type` ENUM('string', 'integer', 'boolean', 'json') NOT NULL DEFAULT 'string',
    `description` VARCHAR(255) NULL,
    `is_system` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '1=System setting, 0=User setting',
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default settings
INSERT INTO `fingerprint_import_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES
('auto_import_enabled', '0', 'boolean', 'Enable automatic import from fingerprint machine', 1, NOW(), NOW()),
('auto_import_interval', '300', 'integer', 'Auto import interval in seconds (default: 5 minutes)', 1, NOW(), NOW()),
('import_batch_size', '1000', 'integer', 'Number of records to process in each batch', 1, NOW(), NOW()),
('duplicate_handling', 'skip', 'string', 'How to handle duplicate records (skip, update, error)', 1, NOW(), NOW()),
('default_status', '1', 'integer', 'Default status for imported attendance records', 1, NOW(), NOW()),
('log_retention_days', '30', 'integer', 'Number of days to keep import logs', 1, NOW(), NOW()),
('verify_student_exists', '1', 'boolean', 'Verify that student exists before importing attendance', 1, NOW(), NOW()),
('create_missing_students', '0', 'boolean', 'Automatically create missing students during import', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = NOW();

-- Show created tables
SHOW TABLES LIKE 'fingerprint%';
