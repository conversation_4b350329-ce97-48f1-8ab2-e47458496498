{"date": "Mon Jun 09 2025 00:43:55 GMT+0700 (Western Indonesia Time)", "error": {}, "level": "error", "message": "unhandledRejection: logger.trace is not a function\nTypeError: logger.trace is not a function\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:42:72)\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:96:48)\n    at assertSessions (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:171:51)\n    at C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:405:23\n    at Object.transaction (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:135:32)\n    at relayMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:294:30)\n    at sendPeerDataOperationMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:218:29)\n    at requestPlaceholderResend (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:744:16)\n    at async sendRetryRequest (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:99:27)\n    at async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:653:33", "os": {"loadavg": [0, 0, 0], "uptime": 606224.703}, "process": {"argv": ["C:\\Program Files\\nodejs\\node.exe", "C:\\laragon\\www\\studentfinger\\wamd_apps\\index.js"], "cwd": "C:\\laragon\\www\\studentfinger\\wamd_apps", "execPath": "C:\\Program Files\\nodejs\\node.exe", "gid": null, "memoryUsage": {"arrayBuffers": 851120, "external": 4474030, "heapTotal": 43544576, "heapUsed": 41488464, "rss": 90529792}, "pid": 28836, "uid": null, "version": "v22.16.0"}, "rejection": true, "service": "whatsapp-gateway", "stack": "TypeError: logger.trace is not a function\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:42:72)\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:96:48)\n    at assertSessions (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:171:51)\n    at C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:405:23\n    at Object.transaction (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:135:32)\n    at relayMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:294:30)\n    at sendPeerDataOperationMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:218:29)\n    at requestPlaceholderResend (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:744:16)\n    at async sendRetryRequest (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:99:27)\n    at async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:653:33", "timestamp": "2025-06-09 00:43:55", "trace": [{"column": 72, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js", "function": "Object.get", "line": 42, "method": "get", "native": false}, {"column": 48, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js", "function": "Object.get", "line": 96, "method": "get", "native": false}, {"column": 51, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js", "function": "assertSessions", "line": 171, "method": null, "native": false}, {"column": 23, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js", "function": null, "line": 405, "method": null, "native": false}, {"column": 32, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js", "function": "Object.transaction", "line": 135, "method": "transaction", "native": false}, {"column": 30, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js", "function": "relayMessage", "line": 294, "method": null, "native": false}, {"column": 29, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js", "function": "sendPeerDataOperationMessage", "line": 218, "method": null, "native": false}, {"column": 16, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js", "function": "requestPlaceholderResend", "line": 744, "method": null, "native": false}, {"column": 27, "file": "C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js", "function": "async sendRetryRequest", "line": 99, "method": null, "native": false}, {"column": 33, "file": "async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js", "function": null, "line": 653, "method": null, "native": false}]}