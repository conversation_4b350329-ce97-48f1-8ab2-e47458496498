<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Multi-Device Gateway</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 800px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .btn-custom {
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="main-container">
                    <div class="text-center mb-5">
                        <h1 class="text-primary mb-3">
                            <i class="fab fa-whatsapp me-2"></i>
                            WhatsApp Multi-Device Gateway
                        </h1>
                        <p class="lead text-muted">Student Attendance System Integration</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <h4 class="text-success mb-3">
                                    <i class="fas fa-qrcode me-2"></i>
                                    QR Code Scanner
                                </h4>
                                <p class="text-muted mb-3">Connect your WhatsApp device by scanning QR code</p>
                                <a href="/qr" class="btn btn-success btn-custom">
                                    <i class="fas fa-mobile-alt me-1"></i>
                                    Connect Device
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <h4 class="text-info mb-3">
                                    <i class="fas fa-heartbeat me-2"></i>
                                    System Status
                                </h4>
                                <p class="text-muted mb-3">Check gateway health and connection status</p>
                                <a href="/health" class="btn btn-info btn-custom">
                                    <i class="fas fa-chart-line me-1"></i>
                                    View Status
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <h4 class="text-warning mb-3">
                                    <i class="fas fa-code me-2"></i>
                                    API Documentation
                                </h4>
                                <p class="text-muted mb-3">Access API endpoints for integration</p>
                                <a href="/api/status" class="btn btn-warning btn-custom">
                                    <i class="fas fa-book me-1"></i>
                                    API Docs
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <h4 class="text-danger mb-3">
                                    <i class="fas fa-webhook me-2"></i>
                                    Webhook Testing
                                </h4>
                                <p class="text-muted mb-3">Test webhook endpoints and integrations</p>
                                <a href="/webhook/test" class="btn btn-danger btn-custom">
                                    <i class="fas fa-flask me-1"></i>
                                    Test Webhook
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-5 text-center">
                        <h5 class="mb-3">Quick API Endpoints</h5>
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <code>/api/status</code>
                                <small class="d-block text-muted">Get Status</small>
                            </div>
                            <div class="col-md-3 mb-2">
                                <code>/api/send-message</code>
                                <small class="d-block text-muted">Send Message</small>
                            </div>
                            <div class="col-md-3 mb-2">
                                <code>/webhook/send</code>
                                <small class="d-block text-muted">Webhook Send</small>
                            </div>
                            <div class="col-md-3 mb-2">
                                <code>/qr/json</code>
                                <small class="d-block text-muted">QR Code Data</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Powered by @WhiskeySockets/Baileys • Node.js • Express.js
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
