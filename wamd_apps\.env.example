# WhatsApp Multi-Device Gateway Configuration

# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# Database Configuration (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=studentfinger

# WhatsApp Configuration
WA_SESSION_NAME=student_attendance_session
WA_AUTO_RECONNECT=true
WA_PRINT_QR_IN_TERMINAL=true
WA_MARK_ONLINE_ON_CONNECT=true

# API Configuration
API_KEY=your_secure_api_key_here
WEBHOOK_URL=http://localhost/studentfinger/whatsappintegration/webhook/baileys

# CodeIgniter Integration
CI_BASE_URL=http://localhost/studentfinger
CI_API_ENDPOINT=/wa/webhook

# Security
JWT_SECRET=your_jwt_secret_here
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# File Upload
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Message Queue
QUEUE_PROCESSING_INTERVAL=5000
MAX_RETRY_ATTEMPTS=3
MESSAGE_DELAY_MS=1000
