{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:43:16","url":"http://localhost/studentfinger/whatsappintegration/webhook/baileys"}
{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:43:16","url":"http://localhost/studentfinger/wa/webhook"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Failed to log activity: Unknown column 'device_id' in 'field list'","service":"whatsapp-gateway","sql":"\n                INSERT INTO wa_logs (\n                    device_id, action, data, created_at\n                ) VALUES (?, ?, ?, NOW())\n            ","sqlMessage":"Unknown column 'device_id' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'device_id' in 'field list'\n    at PromisePool.execute (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at DatabaseService.logActivity (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\DatabaseService.js:254:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async WebhookService.sendStatusUpdate (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WebhookService.js:26:17)\n    at async WhatsAppService.handleDisconnection (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:225:17)\n    at async EventEmitter.<anonymous> (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:84:17)","timestamp":"2025-06-09 00:43:16"}
{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:43:54","url":"http://localhost/studentfinger/whatsappintegration/webhook/baileys"}
{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:43:54","url":"http://localhost/studentfinger/wa/webhook"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Failed to log activity: Unknown column 'device_id' in 'field list'","service":"whatsapp-gateway","sql":"\n                INSERT INTO wa_logs (\n                    device_id, action, data, created_at\n                ) VALUES (?, ?, ?, NOW())\n            ","sqlMessage":"Unknown column 'device_id' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'device_id' in 'field list'\n    at PromisePool.execute (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at DatabaseService.logActivity (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\DatabaseService.js:254:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async WebhookService.sendStatusUpdate (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WebhookService.js:26:17)\n    at async WhatsAppService.handleConnection (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:184:17)\n    at async EventEmitter.<anonymous> (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:86:17)","timestamp":"2025-06-09 00:43:54"}
{"date":"Mon Jun 09 2025 00:43:55 GMT+0700 (Western Indonesia Time)","error":{},"level":"error","message":"unhandledRejection: logger.trace is not a function\nTypeError: logger.trace is not a function\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:42:72)\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:96:48)\n    at assertSessions (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:171:51)\n    at C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:405:23\n    at Object.transaction (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:135:32)\n    at relayMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:294:30)\n    at sendPeerDataOperationMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:218:29)\n    at requestPlaceholderResend (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:744:16)\n    at async sendRetryRequest (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:99:27)\n    at async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:653:33","os":{"loadavg":[0,0,0],"uptime":606224.703},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","C:\\laragon\\www\\studentfinger\\wamd_apps\\index.js"],"cwd":"C:\\laragon\\www\\studentfinger\\wamd_apps","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":851120,"external":4474030,"heapTotal":43544576,"heapUsed":41488464,"rss":90529792},"pid":28836,"uid":null,"version":"v22.16.0"},"rejection":true,"service":"whatsapp-gateway","stack":"TypeError: logger.trace is not a function\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:42:72)\n    at Object.get (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:96:48)\n    at assertSessions (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:171:51)\n    at C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:405:23\n    at Object.transaction (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js:135:32)\n    at relayMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:294:30)\n    at sendPeerDataOperationMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js:218:29)\n    at requestPlaceholderResend (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:744:16)\n    at async sendRetryRequest (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:99:27)\n    at async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js:653:33","timestamp":"2025-06-09 00:43:55","trace":[{"column":72,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js","function":"Object.get","line":42,"method":"get","native":false},{"column":48,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js","function":"Object.get","line":96,"method":"get","native":false},{"column":51,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js","function":"assertSessions","line":171,"method":null,"native":false},{"column":23,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js","function":null,"line":405,"method":null,"native":false},{"column":32,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Utils\\auth-utils.js","function":"Object.transaction","line":135,"method":"transaction","native":false},{"column":30,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js","function":"relayMessage","line":294,"method":null,"native":false},{"column":29,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-send.js","function":"sendPeerDataOperationMessage","line":218,"method":null,"native":false},{"column":16,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js","function":"requestPlaceholderResend","line":744,"method":null,"native":false},{"column":27,"file":"C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js","function":"async sendRetryRequest","line":99,"method":null,"native":false},{"column":33,"file":"async C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\messages-recv.js","function":null,"line":653,"method":null,"native":false}]}
{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:45:26","url":"http://localhost/studentfinger/whatsappintegration/webhook/baileys"}
{"error":"Request failed with status code 404","event":"status_update","level":"error","message":"Failed to send webhook to URL:","service":"whatsapp-gateway","timestamp":"2025-06-09 00:45:26","url":"http://localhost/studentfinger/wa/webhook"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Failed to log activity: Unknown column 'device_id' in 'field list'","service":"whatsapp-gateway","sql":"\n                INSERT INTO wa_logs (\n                    device_id, action, data, created_at\n                ) VALUES (?, ?, ?, NOW())\n            ","sqlMessage":"Unknown column 'device_id' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'device_id' in 'field list'\n    at PromisePool.execute (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at DatabaseService.logActivity (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\DatabaseService.js:254:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async WebhookService.sendStatusUpdate (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WebhookService.js:26:17)\n    at async WhatsAppService.handleDisconnection (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:225:17)\n    at async EventEmitter.<anonymous> (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:84:17)","timestamp":"2025-06-09 00:45:26"}
{"data":null,"isBoom":true,"isServer":false,"level":"error","message":"Failed to disconnect WhatsApp: Connection Closed","output":{"headers":{},"payload":{"error":"Precondition Required","message":"Connection Closed","statusCode":428},"statusCode":428},"service":"whatsapp-gateway","stack":"Error: Connection Closed\n    at sendRawMessage (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\socket.js:60:19)\n    at sendNode (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\socket.js:79:16)\n    at Object.logout (C:\\laragon\\www\\studentfinger\\wamd_apps\\node_modules\\@whiskeysockets\\baileys\\lib\\Socket\\socket.js:347:19)\n    at WhatsAppService.disconnect (C:\\laragon\\www\\studentfinger\\wamd_apps\\src\\services\\WhatsAppService.js:399:33)\n    at WhatsAppGateway.shutdown (C:\\laragon\\www\\studentfinger\\wamd_apps\\index.js:219:44)\n    at process.<anonymous> (C:\\laragon\\www\\studentfinger\\wamd_apps\\index.js:204:45)\n    at process.emit (node:events:518:28)","timestamp":"2025-06-09 00:46:45"}
