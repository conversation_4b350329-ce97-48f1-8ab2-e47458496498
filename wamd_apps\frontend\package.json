{"name": "whatsapp-gateway-frontend", "version": "1.0.0", "description": "Modern React frontend for WhatsApp Multi-Device Gateway", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.2", "web-vitals": "^3.5.0", "socket.io-client": "^4.7.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.155", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "qrcode.react": "^3.1.0", "date-fns": "^2.30.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.8", "prettier": "^3.1.0"}, "proxy": "http://localhost:3000"}