-- Create test students in studentfinger database
USE studentfinger;

-- Insert test students
INSERT INTO `students` (`student_id`, `admission_no`, `firstname`, `lastname`, `mobileno`, `email`, `father_phone`, `rfid`, `status`, `created_at`, `updated_at`) VALUES
(1, 'STD001', '<PERSON>', 'Doe', '081234567890', '<EMAIL>', '081234567891', '1001', 1, NOW(), NOW()),
(2, 'STD002', '<PERSON>', '<PERSON>', '081234567892', '<EMAIL>', '081234567893', '1002', 1, NOW(), NOW()),
(3, 'STD003', 'Bob', '<PERSON>', '081234567894', '<EMAIL>', '081234567895', '1003', 1, NOW(), NOW()),
(4, 'STD004', '<PERSON>', '<PERSON>', '081234567896', '<EMAIL>', '081234567897', '2001', 1, NOW(), NOW()),
(5, 'STD005', '<PERSON>', '<PERSON>', '081234567898', '<EMAIL>', '081234567899', '2002', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `firstname` = VALUES(`firstname`),
    `updated_at` = NOW();

-- Show the students
SELECT student_id, admission_no, firstname, lastname, rfid FROM students ORDER BY student_id;
