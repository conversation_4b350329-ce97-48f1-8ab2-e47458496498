{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "+J1I4oSWEDuYEmlmu6j9KcW5O+BcP64YkxQ6UrFMC34="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "ytehVzlkV3OnAIUqy5PoSCV3xbllIcZrDHJlijrVmUg="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "uGsOq12Nwg7Mj/TMNbL+O25ZYPJF07dF/s8OL88X5FE="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "EYRfX2loIb7hKBkq/LnGCA5S/mVnVxEbESHg62CsKDM="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "WKCM4sjXFkxxdoEpCqTWnMtImWWhtyy2C4Tuacx4Ekw="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "0+Q10aixLrmQUjHAz8KgAOSx8ASPn+zKbzSTrWiMbxk="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "IGTgiQZHhlFvAoBN9ONEl1vefTXlhMIOFKKuEfuZKW8="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "ZP/AZTPQAEvMcXxCytW5UHXgxWjH17hPplBgc8KmllM="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "xpHnfqaH7yiXugLTPEgG+LleSbUlat98Sl6bhh06EpQNNV8VXVFlrBBWdO/AtTzU6poKnvQ1j13svbU29buGCw=="}, "keyId": 1}, "registrationId": 81, "advSecretKey": "kvyDptKkQaeWUhh01fMgE/nO2jzUSIoCvXbTUQ2jCm8=", "processedHistoryMessages": [], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CPCxn4ABELKXl8IGGA4gACgA", "accountSignatureKey": "iKRPUHOAwB3mA9TQ1wt0UnwrJX+xiwfog4sHvMDlfmI=", "accountSignature": "JWlgfIx+dlLeKWmyCgs5pJkv1sVJFQEUtZ43utxDn5EZlbsJZNqHsr/Xqyi3KJ4pmJXKI6AU3YTsbO7y8I00Cg==", "deviceSignature": "/62txCTDzLMZHYUuUT4dmpuAExtDB201H68GJKalb4DF7t/6E7NotVA5PzlLn1EHJPqVzeg2FNotmXExqKw8Bw=="}, "me": {"id": "*************:<EMAIL>", "lid": "**************:77@lid"}, "signalIdentities": [{"identifier": {"name": "*************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BYikT1BzgMAd5gPU0NcLdFJ8KyV/sYsH6IOLB7zA5X5i"}}], "platform": "android", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CA0ICA=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "3R9Z39"}