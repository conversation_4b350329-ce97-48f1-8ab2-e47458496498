-- Create fin_pro database and test data
CREATE DATABASE IF NOT EXISTS fin_pro;
USE fin_pro;

-- Create att_log table (FingerSpot machine format)
CREATE TABLE IF NOT EXISTS `att_log` (
    `sn` VARCHAR(30) NOT NULL,
    `scan_date` DATETIME NOT NULL,
    `pin` VARCHAR(32) NOT NULL,
    `verifymode` INT(11) NOT NULL,
    `inoutmode` INT(11) NOT NULL DEFAULT 0,
    `reserved` INT(11) NOT NULL DEFAULT 0,
    `work_code` INT(11) NOT NULL DEFAULT 0,
    `att_id` VARCHAR(50) NOT NULL DEFAULT '0',
    PRIMARY KEY (`sn`, `scan_date`, `pin`),
    KEY `pin` (`pin`),
    KEY `sn` (`sn`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Insert test data
INSERT INTO `att_log` (`sn`, `scan_date`, `pin`, `verifymode`, `inoutmode`, `reserved`, `work_code`, `att_id`) VALUES
('FIO66205020150662', '2025-01-09 07:15:00', '1001', 1, 1, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 07:16:00', '1002', 20, 1, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 07:17:00', '1003', 3, 1, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 12:00:00', '1001', 1, 2, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 12:01:00', '1002', 20, 2, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 12:02:00', '1003', 3, 2, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 17:00:00', '1001', 1, 2, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 17:01:00', '1002', 20, 2, 0, 0, '0'),
('FIO66205020150662', '2025-01-09 17:02:00', '1003', 3, 2, 0, 0, '0'),
('66208023321907', '2025-01-09 08:00:00', '2001', 20, 1, 0, 0, '0'),
('66208023321907', '2025-01-09 08:01:00', '2002', 1, 1, 0, 0, '0'),
('66208023321907', '2025-01-09 16:30:00', '2001', 20, 2, 0, 0, '0'),
('66208023321907', '2025-01-09 16:31:00', '2002', 1, 2, 0, 0, '0');

-- Show the data
SELECT COUNT(*) as total_records FROM att_log;
SELECT DISTINCT pin FROM att_log ORDER BY pin;
SELECT DISTINCT sn FROM att_log ORDER BY sn;