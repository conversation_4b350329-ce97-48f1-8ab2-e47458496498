-- Create Classroom Notifications Tables

-- Class Sessions Table
CREATE TABLE IF NOT EXISTS `class_sessions` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `class_id` INT(11) UNSIGNED NOT NULL,
    `session_name` VARCHAR(100) NOT NULL,
    `subject` VARCHAR(50) NOT NULL,
    `teacher_name` VARCHAR(100) NOT NULL,
    `start_time` TIME NOT NULL,
    `end_time` TIME NOT NULL,
    `break_duration` INT(11) NOT NULL DEFAULT 15 COMMENT 'Break duration in minutes',
    `status` ENUM('scheduled', 'started', 'break', 'resumed', 'finished', 'cancelled') NOT NULL DEFAULT 'scheduled',
    `actual_start_time` DATETIME NULL,
    `actual_break_time` DATETIME NULL,
    `actual_resume_time` DATETIME NULL,
    `actual_end_time` DATETIME NULL,
    `total_students` INT(11) NOT NULL DEFAULT 0,
    `present_students` INT(11) NOT NULL DEFAULT 0,
    `notifications_sent` INT(11) NOT NULL DEFAULT 0,
    `session_date` DATE NOT NULL,
    `notes` TEXT NULL,
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    `deleted_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    KEY `idx_class_date` (`class_id`, `session_date`),
    KEY `idx_status` (`status`),
    KEY `idx_session_date` (`session_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Notification Templates Table
CREATE TABLE IF NOT EXISTS `notification_templates` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `template_name` VARCHAR(100) NOT NULL,
    `event_type` ENUM('session_start', 'session_break', 'session_resume', 'session_finish', 'attendance_marked') NOT NULL,
    `message_template` TEXT NOT NULL,
    `is_active` TINYINT(1) NOT NULL DEFAULT 1,
    `language` ENUM('id', 'en') NOT NULL DEFAULT 'id',
    `variables` JSON NULL COMMENT 'Available variables for this template',
    `description` VARCHAR(255) NULL,
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    `deleted_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    KEY `idx_event_language` (`event_type`, `language`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Notification Logs Table
CREATE TABLE IF NOT EXISTS `notification_logs` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `session_id` INT(11) UNSIGNED NOT NULL,
    `student_id` INT(11) UNSIGNED NOT NULL,
    `parent_phone` VARCHAR(20) NOT NULL,
    `parent_name` VARCHAR(100) NULL,
    `event_type` ENUM('session_start', 'session_break', 'session_resume', 'session_finish', 'attendance_marked') NOT NULL,
    `template_id` INT(11) UNSIGNED NULL,
    `message_content` TEXT NOT NULL,
    `status` ENUM('pending', 'sent', 'delivered', 'read', 'failed', 'retry') NOT NULL DEFAULT 'pending',
    `wablas_response` JSON NULL COMMENT 'Response from WABLAS API',
    `sent_at` DATETIME NULL,
    `delivered_at` DATETIME NULL,
    `read_at` DATETIME NULL,
    `failed_reason` TEXT NULL,
    `retry_count` INT(11) NOT NULL DEFAULT 0,
    `variables_used` JSON NULL COMMENT 'Variables used in the message',
    `created_at` DATETIME NULL,
    `updated_at` DATETIME NULL,
    PRIMARY KEY (`id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_student_id` (`student_id`),
    KEY `idx_event_status` (`event_type`, `status`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert Default Templates
INSERT INTO `notification_templates` (`template_name`, `event_type`, `message_template`, `is_active`, `language`, `variables`, `description`, `created_at`, `updated_at`) VALUES
('Kelas Dimulai - Default', 'session_start', '🎓 *KELAS DIMULAI*\n\nYth. Orang Tua/Wali {parent_name},\n\nKami informasikan bahwa {student_name} telah hadir di kelas:\n\n📚 *Mata Pelajaran:* {subject}\n🏫 *Kelas:* {class_name}\n👨‍🏫 *Guru:* {teacher_name}\n⏰ *Waktu Mulai:* {start_time}\n📅 *Tanggal:* {session_date}\n\nTerima kasih atas perhatiannya.\n\n*{school_name}*', 1, 'id', JSON_ARRAY('parent_name', 'student_name', 'subject', 'class_name', 'teacher_name', 'start_time', 'session_date', 'school_name'), 'Template notifikasi saat kelas dimulai', NOW(), NOW()),

('Class Started - Default', 'session_start', '🎓 *CLASS STARTED*\n\nDear Parent/Guardian {parent_name},\n\nWe inform you that {student_name} is attending class:\n\n📚 *Subject:* {subject}\n🏫 *Class:* {class_name}\n👨‍🏫 *Teacher:* {teacher_name}\n⏰ *Start Time:* {start_time}\n📅 *Date:* {session_date}\n\nThank you for your attention.\n\n*{school_name}*', 1, 'en', JSON_ARRAY('parent_name', 'student_name', 'subject', 'class_name', 'teacher_name', 'start_time', 'session_date', 'school_name'), 'Notification template when class starts', NOW(), NOW()),

('Istirahat Kelas - Default', 'session_break', '☕ *ISTIRAHAT KELAS*\n\nYth. Orang Tua/Wali {parent_name},\n\nKelas {subject} sedang istirahat:\n\n👤 *Siswa:* {student_name}\n🏫 *Kelas:* {class_name}\n⏰ *Waktu Istirahat:* {break_time}\n⏱️ *Durasi:* {break_duration} menit\n\nKelas akan dilanjutkan setelah istirahat.\n\n*{school_name}*', 1, 'id', JSON_ARRAY('parent_name', 'student_name', 'subject', 'class_name', 'break_time', 'break_duration', 'school_name'), 'Template notifikasi saat kelas istirahat', NOW(), NOW()),

('Kelas Dilanjutkan - Default', 'session_resume', '📚 *KELAS DILANJUTKAN*\n\nYth. Orang Tua/Wali {parent_name},\n\nKelas {subject} telah dilanjutkan setelah istirahat:\n\n👤 *Siswa:* {student_name}\n🏫 *Kelas:* {class_name}\n⏰ *Waktu Lanjut:* {resume_time}\n\nTerima kasih atas perhatiannya.\n\n*{school_name}*', 1, 'id', JSON_ARRAY('parent_name', 'student_name', 'subject', 'class_name', 'resume_time', 'school_name'), 'Template notifikasi saat kelas dilanjutkan', NOW(), NOW()),

('Kelas Selesai - Default', 'session_finish', '✅ *KELAS SELESAI*\n\nYth. Orang Tua/Wali {parent_name},\n\nKelas {subject} telah selesai:\n\n👤 *Siswa:* {student_name}\n🏫 *Kelas:* {class_name}\n👨‍🏫 *Guru:* {teacher_name}\n⏰ *Waktu Selesai:* {end_time}\n⏱️ *Durasi Total:* {total_duration}\n\n{student_name} dapat dijemput atau pulang sesuai jadwal.\n\n*{school_name}*', 1, 'id', JSON_ARRAY('parent_name', 'student_name', 'subject', 'class_name', 'teacher_name', 'end_time', 'total_duration', 'school_name'), 'Template notifikasi saat kelas selesai', NOW(), NOW());

-- Insert Sample Class Session
INSERT INTO `class_sessions` (`class_id`, `session_name`, `subject`, `teacher_name`, `start_time`, `end_time`, `break_duration`, `status`, `session_date`, `total_students`, `present_students`, `notifications_sent`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'Matematika Pagi', 'Matematika', 'Mrs. Sari', '08:00:00', '10:00:00', 15, 'scheduled', CURDATE(), 25, 0, 0, 'Sesi matematika untuk kelas pagi', NOW(), NOW()),
(1, 'Bahasa Indonesia', 'Bahasa Indonesia', 'Mr. Ahmad', '10:30:00', '12:00:00', 10, 'scheduled', CURDATE(), 25, 0, 0, 'Sesi bahasa Indonesia', NOW(), NOW()),
(2, 'IPA Terpadu', 'IPA', 'Mrs. Dewi', '13:00:00', '15:00:00', 20, 'scheduled', CURDATE(), 30, 0, 0, 'Sesi IPA terpadu siang', NOW(), NOW());
