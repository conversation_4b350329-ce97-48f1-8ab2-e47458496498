-- Sample data for studentfinger application

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS studentfinger;
USE studentfinger;

-- Create basic tables if they don't exist
CREATE TABLE IF NOT EXISTS `classes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `class` varchar(100) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `sections` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `class_id` int(11) unsigned DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `sessions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT 'Active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `students` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `student_id` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `rfid_card` varchar(50) DEFAULT NULL,
  `pin` varchar(32) DEFAULT NULL,
  `parent_name` varchar(100) DEFAULT NULL,
  `parent_phone` varchar(20) DEFAULT NULL,
  `parent_email` varchar(100) DEFAULT NULL,
  `emergency_contact` varchar(20) DEFAULT NULL,
  `blood_group` varchar(10) DEFAULT NULL,
  `medical_info` text,
  `admission_date` date DEFAULT NULL,
  `class_id` int(11) unsigned DEFAULT NULL,
  `section_id` int(11) unsigned DEFAULT NULL,
  `session_id` int(11) unsigned DEFAULT NULL,
  `status` enum('Active','Inactive','Graduated','Transferred') DEFAULT 'Active',
  `notes` text,
  `photo` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `att_log` (
  `sn` varchar(30) NOT NULL,
  `scan_date` datetime NOT NULL,
  `pin` varchar(32) NOT NULL,
  `verifymode` int(11) NOT NULL,
  `inoutmode` int(11) DEFAULT NULL,
  `reserved` int(11) DEFAULT NULL,
  `work_code` int(11) DEFAULT NULL,
  `att_id` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`sn`,`scan_date`,`pin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data
INSERT IGNORE INTO `sessions` (`id`, `name`, `start_date`, `end_date`, `status`, `created_at`, `updated_at`) VALUES
(1, '2024-2025', '2024-07-01', '2025-06-30', 'Active', NOW(), NOW());

INSERT IGNORE INTO `classes` (`id`, `class`, `created_at`, `updated_at`) VALUES
(1, 'X', NOW(), NOW()),
(2, 'XI', NOW(), NOW()),
(3, 'XII', NOW(), NOW());

INSERT IGNORE INTO `sections` (`id`, `name`, `class_id`, `created_at`, `updated_at`) VALUES
(1, 'A', 1, NOW(), NOW()),
(2, 'B', 1, NOW(), NOW()),
(3, 'A', 2, NOW(), NOW()),
(4, 'B', 2, NOW(), NOW()),
(5, 'A', 3, NOW(), NOW()),
(6, 'B', 3, NOW(), NOW());

INSERT IGNORE INTO `students` (`id`, `student_id`, `name`, `email`, `phone`, `gender`, `pin`, `parent_name`, `parent_phone`, `class_id`, `section_id`, `session_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 'STD001', 'John Doe', '<EMAIL>', '1234567890', 'Male', '001', 'Mr. Doe', '9876543210', 1, 1, 1, 'Active', NOW(), NOW()),
(2, 'STD002', 'Jane Smith', '<EMAIL>', '1234567891', 'Female', '002', 'Mrs. Smith', '9876543211', 1, 1, 1, 'Active', NOW(), NOW()),
(3, 'STD003', 'Bob Johnson', '<EMAIL>', '1234567892', 'Male', '003', 'Mr. Johnson', '9876543212', 2, 3, 1, 'Active', NOW(), NOW()),
(4, 'STD004', 'Alice Brown', '<EMAIL>', '1234567893', 'Female', '004', 'Mrs. Brown', '9876543213', 2, 3, 1, 'Active', NOW(), NOW()),
(5, 'STD005', 'Charlie Wilson', '<EMAIL>', '1234567894', 'Male', '005', 'Mr. Wilson', '9876543214', 3, 5, 1, 'Active', NOW(), NOW());

-- Insert sample attendance logs
INSERT IGNORE INTO `att_log` (`sn`, `scan_date`, `pin`, `verifymode`, `inoutmode`, `reserved`, `work_code`, `att_id`) VALUES
('FP001', NOW() - INTERVAL 1 HOUR, '001', 1, 0, 0, 0, 'ATT001'),
('FP001', NOW() - INTERVAL 2 HOURS, '002', 1, 0, 0, 0, 'ATT002'),
('FP001', NOW() - INTERVAL 3 HOURS, '003', 1, 0, 0, 0, 'ATT003'),
('FP001', NOW() - INTERVAL 4 HOURS, '004', 1, 0, 0, 0, 'ATT004'),
('FP001', NOW() - INTERVAL 5 HOURS, '005', 1, 0, 0, 0, 'ATT005');
