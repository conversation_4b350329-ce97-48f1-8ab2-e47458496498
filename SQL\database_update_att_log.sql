-- Update att_log table to match FingerSpot fingerprint machine standard
-- This will add missing columns and modify existing ones to ensure seamless import

USE studentfinger;

-- First, let's backup the existing data structure
CREATE TABLE IF NOT EXISTS att_log_backup AS SELECT * FROM att_log;

-- Add missing columns to match FingerSpot standard
ALTER TABLE att_log 
ADD COLUMN IF NOT EXISTS sn VARCHAR(30) NOT NULL DEFAULT '' COMMENT 'Serial number of device',
ADD COLUMN IF NOT EXISTS inoutmode INT(11) NOT NULL DEFAULT 0 COMMENT '0=Check In, 1=Check In, 2=Check Out, 3=Break Out, 4=Break In, 5=Overtime In, 6=Overtime Out',
ADD COLUMN IF NOT EXISTS reserved INT(11) NOT NULL DEFAULT 0 COMMENT 'Reserved field for future use',
ADD COLUMN IF NOT EXISTS work_code INT(11) NOT NULL DEFAULT 0 COMMENT 'Work code for different work types';

-- Modify existing columns to match FingerSpot standard
ALTER TABLE att_log 
MODIFY COLUMN pin VARCHAR(32) NOT NULL COMMENT 'Employee PIN/ID',
MODIFY COLUMN verifymode INT(11) NOT NULL COMMENT '1=Fingerprint, 3=RFID Card, 20=Face Recognition',
MODIFY COLUMN att_id VARCHAR(50) NOT NULL DEFAULT '0' COMMENT 'Attendance ID from device';

-- Update the primary key to match FingerSpot standard (composite key)
-- First drop the existing primary key
ALTER TABLE att_log DROP PRIMARY KEY;

-- Add the new composite primary key
ALTER TABLE att_log ADD PRIMARY KEY (sn, scan_date, pin);

-- Add indexes for better performance
ALTER TABLE att_log ADD INDEX idx_pin (pin);
ALTER TABLE att_log ADD INDEX idx_sn (sn);
ALTER TABLE att_log ADD INDEX idx_scan_date (scan_date);
ALTER TABLE att_log ADD INDEX idx_verifymode (verifymode);
ALTER TABLE att_log ADD INDEX idx_inoutmode (inoutmode);

-- Update existing records to have default values for new columns
UPDATE att_log SET 
    sn = COALESCE(serialnumber, 'DEFAULT_DEVICE'),
    inoutmode = COALESCE(status, 1),
    reserved = 0,
    work_code = 0
WHERE sn = '' OR sn IS NULL;

-- Create a view for backward compatibility with the old structure
CREATE OR REPLACE VIEW att_log_legacy AS
SELECT 
    att_id,
    pin,
    scan_date,
    verifymode,
    inoutmode as status,
    sn as serialnumber,
    student_id,
    created_at,
    updated_at,
    deleted_at
FROM att_log;

-- Create a mapping table for inoutmode to status conversion
CREATE TABLE IF NOT EXISTS attendance_mode_mapping (
    inoutmode INT(11) PRIMARY KEY,
    status_name VARCHAR(50) NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert standard FingerSpot mode mappings
INSERT IGNORE INTO attendance_mode_mapping (inoutmode, status_name, description) VALUES
(0, 'Check In', 'Employee check in'),
(1, 'Check In', 'Employee check in (alternative)'),
(2, 'Check Out', 'Employee check out'),
(3, 'Break Out', 'Employee going for break'),
(4, 'Break In', 'Employee returning from break'),
(5, 'Overtime In', 'Employee starting overtime'),
(6, 'Overtime Out', 'Employee ending overtime');

-- Create a function to convert inoutmode to readable status
DELIMITER //
CREATE FUNCTION IF NOT EXISTS GetAttendanceStatus(inout_mode INT) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE status_result VARCHAR(50);
    
    SELECT status_name INTO status_result 
    FROM attendance_mode_mapping 
    WHERE inoutmode = inout_mode;
    
    IF status_result IS NULL THEN
        SET status_result = 'Unknown';
    END IF;
    
    RETURN status_result;
END//
DELIMITER ;

-- Create a function to convert verifymode to readable method
DELIMITER //
CREATE FUNCTION IF NOT EXISTS GetVerifyMethod(verify_mode INT) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE method_result VARCHAR(50);
    
    CASE verify_mode
        WHEN 1 THEN SET method_result = 'Fingerprint';
        WHEN 3 THEN SET method_result = 'RFID Card';
        WHEN 20 THEN SET method_result = 'Face Recognition';
        ELSE SET method_result = 'Unknown';
    END CASE;
    
    RETURN method_result;
END//
DELIMITER ;

-- Create an enhanced view with readable status and method names
CREATE OR REPLACE VIEW att_log_enhanced AS
SELECT 
    sn,
    scan_date,
    pin,
    verifymode,
    GetVerifyMethod(verifymode) as verify_method,
    inoutmode,
    GetAttendanceStatus(inoutmode) as attendance_status,
    reserved,
    work_code,
    att_id,
    -- Legacy fields for backward compatibility
    att_id as legacy_att_id,
    pin as legacy_pin,
    scan_date as legacy_scan_date,
    verifymode as legacy_verifymode,
    inoutmode as legacy_status,
    sn as legacy_serialnumber,
    NULL as legacy_student_id,
    NULL as legacy_created_at,
    NULL as legacy_updated_at,
    NULL as legacy_deleted_at
FROM att_log;

-- Show the updated table structure
DESCRIBE att_log;

-- Show sample data with the new structure
SELECT 'Sample data after update:' as info;
SELECT * FROM att_log_enhanced LIMIT 5;

-- Show the mapping table
SELECT 'Attendance mode mappings:' as info;
SELECT * FROM attendance_mode_mapping;

COMMIT;
